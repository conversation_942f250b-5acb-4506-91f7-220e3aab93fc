<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Name - Portfolio</title>
    <meta name="description" content="Personal portfolio of [Your Name] - Full Stack Developer">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#000000',
                        'dark-secondary': '#0f0f0f',
                        'dark-accent': '#1a1a1a',
                        'gold': '#D4AF37',
                        'gold-light': '#F7E98E',
                        'gold-dark': '#B8860B',
                        'premium-white': '#FAFAFA',
                        'soft-white': '#F5F5F5',
                    },
                    fontFamily: {
                        'sans': ['Playfair Display', 'serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'float': 'float 3s ease-in-out infinite',
                        'shimmer': 'shimmer 2s linear infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        @keyframes glow {
            from { text-shadow: 0 0 10px #D4AF37, 0 0 20px #D4AF37; }
            to { text-shadow: 0 0 20px #D4AF37, 0 0 30px #D4AF37; }
        }

        .gradient-text {
            background: linear-gradient(135deg, #D4AF37, #F7E98E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .premium-gradient {
            background: linear-gradient(135deg, #FAFAFA 0%, #D4AF37 50%, #FAFAFA 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-effect {
            background: rgba(0, 0, 0, 0.85);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .hover-glow:hover {
            box-shadow: 0 0 30px rgba(212, 175, 55, 0.3), 0 0 60px rgba(212, 175, 55, 0.1);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }

        .premium-border {
            border: 1px solid transparent;
            background: linear-gradient(#000, #000) padding-box,
                        linear-gradient(135deg, #D4AF37, #FAFAFA) border-box;
        }

        .shimmer-text {
            background: linear-gradient(90deg, #FAFAFA 25%, #D4AF37 50%, #FAFAFA 75%);
            background-size: 200% 100%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: shimmer 3s linear infinite;
        }
    </style>
</head>
<body class="bg-dark-bg text-premium-white font-body overflow-x-hidden">
    
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-effect transition-all duration-300" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="#home" class="text-2xl font-bold font-sans gradient-text tracking-wider">PORTFOLIO</a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#home" class="nav-link text-soft-white hover:text-gold transition-colors duration-300 font-medium tracking-wide">Home</a>
                        <a href="#about" class="nav-link text-soft-white hover:text-gold transition-colors duration-300 font-medium tracking-wide">About</a>
                        <a href="#projects" class="nav-link text-soft-white hover:text-gold transition-colors duration-300 font-medium tracking-wide">Projects</a>
                        <a href="#skills" class="nav-link text-soft-white hover:text-gold transition-colors duration-300 font-medium tracking-wide">Skills</a>
                        <a href="#contact" class="nav-link text-soft-white hover:text-gold transition-colors duration-300 font-medium tracking-wide">Contact</a>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-btn text-soft-white hover:text-gold focus:outline-none focus:text-gold">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="md:hidden mobile-menu hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 bg-dark-secondary border-t border-gold">
                <a href="#home" class="mobile-nav-link block px-3 py-2 text-soft-white hover:text-gold transition-colors duration-300 font-medium">Home</a>
                <a href="#about" class="mobile-nav-link block px-3 py-2 text-soft-white hover:text-gold transition-colors duration-300 font-medium">About</a>
                <a href="#projects" class="mobile-nav-link block px-3 py-2 text-soft-white hover:text-gold transition-colors duration-300 font-medium">Projects</a>
                <a href="#skills" class="mobile-nav-link block px-3 py-2 text-soft-white hover:text-gold transition-colors duration-300 font-medium">Skills</a>
                <a href="#contact" class="mobile-nav-link block px-3 py-2 text-soft-white hover:text-gold transition-colors duration-300 font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-15">
            <div class="absolute top-20 left-20 w-72 h-72 bg-gold rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
            <div class="absolute top-40 right-20 w-72 h-72 bg-premium-white rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-20 left-1/2 w-96 h-96 bg-gold-light rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 4s;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div class="animate-fade-in">
                <h1 class="text-6xl md:text-8xl font-bold mb-6 font-sans tracking-tight">
                    Hi, I'm <span class="shimmer-text">Your Name</span>
                </h1>
                <p class="text-2xl md:text-3xl text-gold mb-4 max-w-4xl mx-auto font-light tracking-wide">
                    Premium Full Stack Developer
                </p>
                <p class="text-lg md:text-xl text-soft-white mb-12 max-w-3xl mx-auto opacity-90">
                    Crafting exceptional digital experiences with elegance and precision
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="#projects" class="premium-border bg-gradient-to-r from-gold to-gold-light text-black px-10 py-4 rounded-lg font-semibold transition-all duration-300 hover-glow tracking-wide uppercase">
                        View My Work
                    </a>
                    <a href="#contact" class="border-2 border-premium-white hover:border-gold text-premium-white hover:text-gold px-10 py-4 rounded-lg font-semibold transition-all duration-300 hover:bg-premium-white hover:text-black tracking-wide uppercase">
                        Get In Touch
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <i class="fas fa-chevron-down text-gold text-2xl"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-dark-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-bold mb-6 font-sans">About <span class="premium-gradient">Me</span></h2>
                <p class="text-soft-white text-xl max-w-3xl mx-auto font-light">
                    Discover the passion and expertise behind premium digital craftsmanship
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-12 items-center">
                <!-- Photo -->
                <div class="text-center">
                    <div class="w-80 h-80 mx-auto premium-border bg-dark-accent rounded-2xl flex items-center justify-center mb-6 hover-glow transition-all duration-300">
                        <i class="fas fa-user text-6xl text-premium-white"></i>
                        <p class="absolute mt-32 text-sm text-gold">Your Photo Here</p>
                    </div>
                </div>

                <!-- Content -->
                <div class="space-y-8">
                    <h3 class="text-3xl font-bold mb-6 font-sans text-premium-white">Premium Full Stack Developer & Digital Architect</h3>
                    <p class="text-soft-white leading-relaxed text-lg">
                        I'm a passionate developer specializing in creating premium web applications from concept to deployment.
                        I transform complex challenges into elegant, sophisticated, and intuitive digital solutions.
                    </p>
                    <p class="text-soft-white leading-relaxed text-lg">
                        When I'm not crafting code, you'll find me exploring cutting-edge technologies, contributing to open-source projects,
                        and sharing expertise with the global developer community.
                    </p>

                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 gap-6 mt-10">
                        <div class="text-center p-6 premium-border bg-dark-accent rounded-xl hover-glow transition-all duration-300">
                            <h4 class="text-3xl font-bold gradient-text mb-2">50+</h4>
                            <p class="text-premium-white font-medium">Premium Projects</p>
                        </div>
                        <div class="text-center p-6 premium-border bg-dark-accent rounded-xl hover-glow transition-all duration-300">
                            <h4 class="text-3xl font-bold gradient-text mb-2">3+</h4>
                            <p class="text-premium-white font-medium">Years Excellence</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-bold mb-6 font-sans">My <span class="premium-gradient">Projects</span></h2>
                <p class="text-soft-white text-xl max-w-3xl mx-auto font-light">
                    Showcasing premium digital solutions crafted with excellence
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project Card Template -->
                <div class="premium-border bg-dark-secondary rounded-xl overflow-hidden hover-glow transition-all duration-300 group">
                    <div class="h-48 bg-dark-accent flex items-center justify-center border-b border-gold">
                        <i class="fas fa-image text-4xl text-premium-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3 group-hover:text-gold transition-colors duration-300 text-premium-white">Premium E-Commerce Platform</h3>
                        <p class="text-soft-white mb-4 leading-relaxed">Luxury e-commerce solution with advanced features and premium user experience.</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gold text-black text-sm rounded-full font-medium">React</span>
                            <span class="px-3 py-1 bg-gold text-black text-sm rounded-full font-medium">Node.js</span>
                            <span class="px-3 py-1 bg-gold text-black text-sm rounded-full font-medium">MongoDB</span>
                        </div>
                        <div class="flex gap-4">
                            <a href="#" class="text-gold hover:text-gold-light transition-colors duration-300 font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Live Demo
                            </a>
                            <a href="#" class="text-gold hover:text-gold-light transition-colors duration-300 font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Second Project -->
                <div class="premium-border bg-dark-secondary rounded-xl overflow-hidden hover-glow transition-all duration-300 group">
                    <div class="h-48 bg-dark-accent flex items-center justify-center border-b border-gold">
                        <i class="fas fa-image text-4xl text-premium-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3 group-hover:text-gold transition-colors duration-300 text-premium-white">Elite Task Management</h3>
                        <p class="text-soft-white mb-4 leading-relaxed">Premium task management solution with real-time collaboration and elegant design.</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-premium-white text-black text-sm rounded-full font-medium">Vue.js</span>
                            <span class="px-3 py-1 bg-premium-white text-black text-sm rounded-full font-medium">Python</span>
                            <span class="px-3 py-1 bg-premium-white text-black text-sm rounded-full font-medium">PostgreSQL</span>
                        </div>
                        <div class="flex gap-4">
                            <a href="#" class="text-premium-white hover:text-gold transition-colors duration-300 font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Live Demo
                            </a>
                            <a href="#" class="text-premium-white hover:text-gold transition-colors duration-300 font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Third Project -->
                <div class="premium-border bg-dark-secondary rounded-xl overflow-hidden hover-glow transition-all duration-300 group">
                    <div class="h-48 bg-dark-accent flex items-center justify-center border-b border-gold">
                        <i class="fas fa-image text-4xl text-premium-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-3 group-hover:text-gold transition-colors duration-300 text-premium-white">Luxury Analytics Dashboard</h3>
                        <p class="text-soft-white mb-4 leading-relaxed">Sophisticated analytics platform with advanced data visualization and insights.</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gold text-black text-sm rounded-full font-medium">Angular</span>
                            <span class="px-3 py-1 bg-gold text-black text-sm rounded-full font-medium">Express</span>
                            <span class="px-3 py-1 bg-gold text-black text-sm rounded-full font-medium">MySQL</span>
                        </div>
                        <div class="flex gap-4">
                            <a href="#" class="text-gold hover:text-gold-light transition-colors duration-300 font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Live Demo
                            </a>
                            <a href="#" class="text-gold hover:text-gold-light transition-colors duration-300 font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-dark-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-bold mb-6 font-sans">My <span class="premium-gradient">Skills</span></h2>
                <p class="text-soft-white text-xl max-w-3xl mx-auto font-light">
                    Premium technologies and elite tools for exceptional results
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Frontend -->
                <div class="text-center">
                    <div class="premium-border bg-dark-accent p-8 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-code text-5xl text-gold mb-6"></i>
                        <h3 class="text-2xl font-bold mb-6 text-premium-white">Frontend</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">HTML/CSS</span>
                                <span class="text-gold font-bold">95%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-gold">
                                <div class="bg-gradient-to-r from-gold to-gold-light h-3 rounded-full" style="width: 95%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">JavaScript</span>
                                <span class="text-gold font-bold">90%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-gold">
                                <div class="bg-gradient-to-r from-gold to-gold-light h-3 rounded-full" style="width: 90%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">React</span>
                                <span class="text-gold font-bold">85%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-gold">
                                <div class="bg-gradient-to-r from-gold to-gold-light h-3 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Backend -->
                <div class="text-center">
                    <div class="premium-border bg-dark-accent p-8 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-server text-5xl text-premium-white mb-6"></i>
                        <h3 class="text-2xl font-bold mb-6 text-premium-white">Backend</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">Node.js</span>
                                <span class="text-premium-white font-bold">88%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-premium-white">
                                <div class="bg-gradient-to-r from-premium-white to-soft-white h-3 rounded-full" style="width: 88%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">Python</span>
                                <span class="text-premium-white font-bold">82%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-premium-white">
                                <div class="bg-gradient-to-r from-premium-white to-soft-white h-3 rounded-full" style="width: 82%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">Express</span>
                                <span class="text-premium-white font-bold">85%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-premium-white">
                                <div class="bg-gradient-to-r from-premium-white to-soft-white h-3 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Database -->
                <div class="text-center">
                    <div class="premium-border bg-dark-accent p-8 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-database text-5xl text-gold mb-6"></i>
                        <h3 class="text-2xl font-bold mb-6 text-premium-white">Database</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">MongoDB</span>
                                <span class="text-gold font-bold">80%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-gold">
                                <div class="bg-gradient-to-r from-gold-dark to-gold h-3 rounded-full" style="width: 80%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">PostgreSQL</span>
                                <span class="text-gold font-bold">75%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-gold">
                                <div class="bg-gradient-to-r from-gold-dark to-gold h-3 rounded-full" style="width: 75%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">MySQL</span>
                                <span class="text-gold font-bold">78%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-gold">
                                <div class="bg-gradient-to-r from-gold-dark to-gold h-3 rounded-full" style="width: 78%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tools -->
                <div class="text-center">
                    <div class="premium-border bg-dark-accent p-8 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-tools text-5xl text-premium-white mb-6"></i>
                        <h3 class="text-2xl font-bold mb-6 text-premium-white">Tools</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">Git</span>
                                <span class="text-premium-white font-bold">92%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-premium-white">
                                <div class="bg-gradient-to-r from-gold-light to-premium-white h-3 rounded-full" style="width: 92%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">Docker</span>
                                <span class="text-premium-white font-bold">70%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-premium-white">
                                <div class="bg-gradient-to-r from-gold-light to-premium-white h-3 rounded-full" style="width: 70%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-soft-white font-medium">AWS</span>
                                <span class="text-premium-white font-bold">65%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-3 border border-premium-white">
                                <div class="bg-gradient-to-r from-gold-light to-premium-white h-3 rounded-full" style="width: 65%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-bold mb-6 font-sans">Get In <span class="premium-gradient">Touch</span></h2>
                <p class="text-soft-white text-xl max-w-3xl mx-auto font-light">
                    Ready for premium collaboration? Let's create something extraordinary together
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-12">
                <!-- Contact Info -->
                <div class="space-y-8">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gold p-4 rounded-lg">
                            <i class="fas fa-envelope text-xl text-black"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-premium-white">Email</h3>
                            <p class="text-soft-white"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="bg-premium-white p-4 rounded-lg">
                            <i class="fas fa-phone text-xl text-black"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-premium-white">Phone</h3>
                            <p class="text-soft-white">+****************</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="bg-gold p-4 rounded-lg">
                            <i class="fas fa-map-marker-alt text-xl text-black"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-premium-white">Location</h3>
                            <p class="text-soft-white">Your City, Country</p>
                        </div>
                    </div>

                    <!-- Social Links -->
                    <div class="pt-8">
                        <h3 class="text-xl font-bold mb-6 text-gold-light">Follow Me</h3>
                        <div class="flex space-x-4">
                            <a href="#" class="premium-border bg-dark-accent p-4 rounded-xl hover:bg-gold hover:text-black transition-all duration-300 hover-glow">
                                <i class="fab fa-github text-xl text-gold"></i>
                            </a>
                            <a href="#" class="premium-border bg-dark-accent p-4 rounded-xl hover:bg-gold hover:text-black transition-all duration-300 hover-glow">
                                <i class="fab fa-linkedin text-xl text-gold"></i>
                            </a>
                            <a href="#" class="premium-border bg-dark-accent p-4 rounded-xl hover:bg-gold hover:text-black transition-all duration-300 hover-glow">
                                <i class="fab fa-twitter text-xl text-gold"></i>
                            </a>
                            <a href="#" class="premium-border bg-dark-accent p-4 rounded-xl hover:bg-gold hover:text-black transition-all duration-300 hover-glow">
                                <i class="fab fa-instagram text-xl text-gold"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="bg-dark-secondary p-8 rounded-xl">
                    <form id="contact-form" class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium mb-2">Name</label>
                            <input type="text" id="name" name="name" required
                                   class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium mb-2">Email</label>
                            <input type="email" id="email" name="email" required
                                   class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300">
                        </div>

                        <div>
                            <label for="subject" class="block text-sm font-medium mb-2">Subject</label>
                            <input type="text" id="subject" name="subject" required
                                   class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300">
                        </div>

                        <div>
                            <label for="message" class="block text-sm font-medium mb-2">Message</label>
                            <textarea id="message" name="message" rows="5" required
                                      class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300 resize-none"></textarea>
                        </div>

                        <button type="submit"
                                class="w-full bg-accent-blue hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-all duration-300 hover-glow">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-secondary py-8 border-t border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-400">&copy; 2024 Your Name. All rights reserved.</p>
                </div>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-300">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-300">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
