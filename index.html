<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Name - Portfolio</title>
    <meta name="description" content="Personal portfolio of [Your Name] - Full Stack Developer">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#0a0a0a',
                        'dark-secondary': '#1a1a1a',
                        'dark-accent': '#2a2a2a',
                        'accent-blue': '#3b82f6',
                        'accent-purple': '#8b5cf6',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'float': 'float 3s ease-in-out infinite',
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .glass-effect {
            background: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .hover-glow:hover {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body class="bg-dark-bg text-white font-sans overflow-x-hidden">
    
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-effect transition-all duration-300" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="#home" class="text-xl font-bold gradient-text">Portfolio</a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#home" class="nav-link text-gray-300 hover:text-white transition-colors duration-300">Home</a>
                        <a href="#about" class="nav-link text-gray-300 hover:text-white transition-colors duration-300">About</a>
                        <a href="#projects" class="nav-link text-gray-300 hover:text-white transition-colors duration-300">Projects</a>
                        <a href="#skills" class="nav-link text-gray-300 hover:text-white transition-colors duration-300">Skills</a>
                        <a href="#contact" class="nav-link text-gray-300 hover:text-white transition-colors duration-300">Contact</a>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-btn text-gray-300 hover:text-white focus:outline-none focus:text-white">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="md:hidden mobile-menu hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 bg-dark-secondary">
                <a href="#home" class="mobile-nav-link block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-300">Home</a>
                <a href="#about" class="mobile-nav-link block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-300">About</a>
                <a href="#projects" class="mobile-nav-link block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-300">Projects</a>
                <a href="#skills" class="mobile-nav-link block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-300">Skills</a>
                <a href="#contact" class="mobile-nav-link block px-3 py-2 text-gray-300 hover:text-white transition-colors duration-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-20 left-20 w-72 h-72 bg-accent-blue rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
            <div class="absolute top-40 right-20 w-72 h-72 bg-accent-purple rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 2s;"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div class="animate-fade-in">
                <h1 class="text-5xl md:text-7xl font-bold mb-6">
                    Hi, I'm <span class="gradient-text">Your Name</span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                    Full Stack Developer passionate about creating exceptional digital experiences
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#projects" class="bg-accent-blue hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 hover-glow">
                        View My Work
                    </a>
                    <a href="#contact" class="border border-gray-600 hover:border-white text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 hover:bg-white hover:text-black">
                        Get In Touch
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <i class="fas fa-chevron-down text-gray-400 text-xl"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-dark-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-4">About <span class="gradient-text">Me</span></h2>
                <p class="text-gray-400 text-lg max-w-2xl mx-auto">
                    Get to know more about who I am, what I do, and what skills I have
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <!-- Photo -->
                <div class="text-center">
                    <div class="w-80 h-80 mx-auto bg-dark-accent rounded-2xl flex items-center justify-center mb-6 hover-glow transition-all duration-300">
                        <i class="fas fa-user text-6xl text-gray-500"></i>
                        <p class="absolute mt-32 text-sm text-gray-500">Your Photo Here</p>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="space-y-6">
                    <h3 class="text-2xl font-semibold mb-4">Full Stack Developer & Problem Solver</h3>
                    <p class="text-gray-300 leading-relaxed">
                        I'm a passionate developer with experience in building web applications from concept to deployment. 
                        I enjoy turning complex problems into simple, beautiful, and intuitive solutions.
                    </p>
                    <p class="text-gray-300 leading-relaxed">
                        When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, 
                        or sharing my knowledge with the developer community.
                    </p>
                    
                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 gap-6 mt-8">
                        <div class="text-center p-4 bg-dark-accent rounded-lg">
                            <h4 class="text-2xl font-bold gradient-text">50+</h4>
                            <p class="text-gray-400">Projects Completed</p>
                        </div>
                        <div class="text-center p-4 bg-dark-accent rounded-lg">
                            <h4 class="text-2xl font-bold gradient-text">3+</h4>
                            <p class="text-gray-400">Years Experience</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-4">My <span class="gradient-text">Projects</span></h2>
                <p class="text-gray-400 text-lg max-w-2xl mx-auto">
                    Here are some of the projects I've worked on recently
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project Card Template -->
                <div class="bg-dark-secondary rounded-xl overflow-hidden hover-glow transition-all duration-300 group">
                    <div class="h-48 bg-dark-accent flex items-center justify-center">
                        <i class="fas fa-image text-4xl text-gray-500"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent-blue transition-colors duration-300">Project Name</h3>
                        <p class="text-gray-400 mb-4">Brief description of the project and what technologies were used to build it.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">React</span>
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">Node.js</span>
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">MongoDB</span>
                        </div>
                        <div class="flex gap-4">
                            <a href="#" class="text-accent-blue hover:text-blue-400 transition-colors duration-300">
                                <i class="fas fa-external-link-alt mr-1"></i> Live Demo
                            </a>
                            <a href="#" class="text-accent-blue hover:text-blue-400 transition-colors duration-300">
                                <i class="fab fa-github mr-1"></i> Code
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Duplicate for more projects -->
                <div class="bg-dark-secondary rounded-xl overflow-hidden hover-glow transition-all duration-300 group">
                    <div class="h-48 bg-dark-accent flex items-center justify-center">
                        <i class="fas fa-image text-4xl text-gray-500"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent-blue transition-colors duration-300">Another Project</h3>
                        <p class="text-gray-400 mb-4">Description of another amazing project with different tech stack and features.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">Vue.js</span>
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">Python</span>
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">PostgreSQL</span>
                        </div>
                        <div class="flex gap-4">
                            <a href="#" class="text-accent-blue hover:text-blue-400 transition-colors duration-300">
                                <i class="fas fa-external-link-alt mr-1"></i> Live Demo
                            </a>
                            <a href="#" class="text-accent-blue hover:text-blue-400 transition-colors duration-300">
                                <i class="fab fa-github mr-1"></i> Code
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bg-dark-secondary rounded-xl overflow-hidden hover-glow transition-all duration-300 group">
                    <div class="h-48 bg-dark-accent flex items-center justify-center">
                        <i class="fas fa-image text-4xl text-gray-500"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 group-hover:text-accent-blue transition-colors duration-300">Third Project</h3>
                        <p class="text-gray-400 mb-4">Yet another project showcasing different skills and technologies in web development.</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">Angular</span>
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">Express</span>
                            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">MySQL</span>
                        </div>
                        <div class="flex gap-4">
                            <a href="#" class="text-accent-blue hover:text-blue-400 transition-colors duration-300">
                                <i class="fas fa-external-link-alt mr-1"></i> Live Demo
                            </a>
                            <a href="#" class="text-accent-blue hover:text-blue-400 transition-colors duration-300">
                                <i class="fab fa-github mr-1"></i> Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-dark-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-4">My <span class="gradient-text">Skills</span></h2>
                <p class="text-gray-400 text-lg max-w-2xl mx-auto">
                    Technologies and tools I work with
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Frontend -->
                <div class="text-center">
                    <div class="bg-dark-accent p-6 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-code text-4xl text-accent-blue mb-4"></i>
                        <h3 class="text-xl font-semibold mb-4">Frontend</h3>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span>HTML/CSS</span>
                                <span class="text-accent-blue">95%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 95%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>JavaScript</span>
                                <span class="text-accent-blue">90%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 90%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>React</span>
                                <span class="text-accent-blue">85%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Backend -->
                <div class="text-center">
                    <div class="bg-dark-accent p-6 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-server text-4xl text-accent-purple mb-4"></i>
                        <h3 class="text-xl font-semibold mb-4">Backend</h3>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span>Node.js</span>
                                <span class="text-accent-purple">88%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-accent-purple h-2 rounded-full" style="width: 88%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Python</span>
                                <span class="text-accent-purple">82%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-accent-purple h-2 rounded-full" style="width: 82%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Express</span>
                                <span class="text-accent-purple">85%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-accent-purple h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Database -->
                <div class="text-center">
                    <div class="bg-dark-accent p-6 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-database text-4xl text-green-400 mb-4"></i>
                        <h3 class="text-xl font-semibold mb-4">Database</h3>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span>MongoDB</span>
                                <span class="text-green-400">80%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-green-400 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>PostgreSQL</span>
                                <span class="text-green-400">75%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-green-400 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>MySQL</span>
                                <span class="text-green-400">78%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-green-400 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tools -->
                <div class="text-center">
                    <div class="bg-dark-accent p-6 rounded-xl hover-glow transition-all duration-300 mb-4">
                        <i class="fas fa-tools text-4xl text-yellow-400 mb-4"></i>
                        <h3 class="text-xl font-semibold mb-4">Tools</h3>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span>Git</span>
                                <span class="text-yellow-400">92%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-yellow-400 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Docker</span>
                                <span class="text-yellow-400">70%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-yellow-400 h-2 rounded-full" style="width: 70%"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>AWS</span>
                                <span class="text-yellow-400">65%</span>
                            </div>
                            <div class="w-full bg-dark-bg rounded-full h-2">
                                <div class="bg-yellow-400 h-2 rounded-full" style="width: 65%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-4">Get In <span class="gradient-text">Touch</span></h2>
                <p class="text-gray-400 text-lg max-w-2xl mx-auto">
                    Have a project in mind? Let's work together to bring your ideas to life
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-12">
                <!-- Contact Info -->
                <div class="space-y-8">
                    <div class="flex items-center space-x-4">
                        <div class="bg-accent-blue p-3 rounded-lg">
                            <i class="fas fa-envelope text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Email</h3>
                            <p class="text-gray-400"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="bg-accent-purple p-3 rounded-lg">
                            <i class="fas fa-phone text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Phone</h3>
                            <p class="text-gray-400">+****************</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="bg-green-400 p-3 rounded-lg">
                            <i class="fas fa-map-marker-alt text-xl text-black"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold">Location</h3>
                            <p class="text-gray-400">Your City, Country</p>
                        </div>
                    </div>

                    <!-- Social Links -->
                    <div class="pt-8">
                        <h3 class="text-lg font-semibold mb-4">Follow Me</h3>
                        <div class="flex space-x-4">
                            <a href="#" class="bg-dark-accent p-3 rounded-lg hover:bg-accent-blue transition-colors duration-300">
                                <i class="fab fa-github text-xl"></i>
                            </a>
                            <a href="#" class="bg-dark-accent p-3 rounded-lg hover:bg-accent-blue transition-colors duration-300">
                                <i class="fab fa-linkedin text-xl"></i>
                            </a>
                            <a href="#" class="bg-dark-accent p-3 rounded-lg hover:bg-accent-blue transition-colors duration-300">
                                <i class="fab fa-twitter text-xl"></i>
                            </a>
                            <a href="#" class="bg-dark-accent p-3 rounded-lg hover:bg-accent-blue transition-colors duration-300">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="bg-dark-secondary p-8 rounded-xl">
                    <form id="contact-form" class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium mb-2">Name</label>
                            <input type="text" id="name" name="name" required
                                   class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium mb-2">Email</label>
                            <input type="email" id="email" name="email" required
                                   class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300">
                        </div>

                        <div>
                            <label for="subject" class="block text-sm font-medium mb-2">Subject</label>
                            <input type="text" id="subject" name="subject" required
                                   class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300">
                        </div>

                        <div>
                            <label for="message" class="block text-sm font-medium mb-2">Message</label>
                            <textarea id="message" name="message" rows="5" required
                                      class="w-full px-4 py-3 bg-dark-accent border border-gray-600 rounded-lg focus:outline-none focus:border-accent-blue transition-colors duration-300 resize-none"></textarea>
                        </div>

                        <button type="submit"
                                class="w-full bg-accent-blue hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-all duration-300 hover-glow">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-secondary py-8 border-t border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-400">&copy; 2024 Your Name. All rights reserved.</p>
                </div>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-300">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-300">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
