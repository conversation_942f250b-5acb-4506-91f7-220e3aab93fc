/* Custom CSS for Portfolio Website */
/* This file contains additional styles that complement Tailwind CSS */

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
}

/* Selection color */
::selection {
    background: #3b82f6;
    color: white;
}

::-moz-selection {
    background: #3b82f6;
    color: white;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
textarea:focus,
a:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Custom button hover effects */
.btn-primary {
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Glitch effect for special elements */
.glitch {
    position: relative;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch::before {
    animation: glitch-1 0.5s infinite;
    color: #ff0000;
    z-index: -1;
}

.glitch::after {
    animation: glitch-2 0.5s infinite;
    color: #00ff00;
    z-index: -2;
}

@keyframes glitch-1 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

@keyframes glitch-2 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(2px, 2px); }
    40% { transform: translate(2px, -2px); }
    60% { transform: translate(-2px, 2px); }
    80% { transform: translate(-2px, -2px); }
}

/* Particle background effect */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #3b82f6;
    border-radius: 50%;
    animation: float-particle 10s infinite linear;
}

@keyframes float-particle {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Loading spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #1a1a1a;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pulse animation for call-to-action buttons */
.pulse-btn {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Text reveal animation */
.text-reveal {
    overflow: hidden;
}

.text-reveal span {
    display: inline-block;
    transform: translateY(100%);
    animation: reveal-text 0.8s ease-out forwards;
}

@keyframes reveal-text {
    to {
        transform: translateY(0);
    }
}

/* Stagger animation delays */
.text-reveal span:nth-child(1) { animation-delay: 0.1s; }
.text-reveal span:nth-child(2) { animation-delay: 0.2s; }
.text-reveal span:nth-child(3) { animation-delay: 0.3s; }
.text-reveal span:nth-child(4) { animation-delay: 0.4s; }
.text-reveal span:nth-child(5) { animation-delay: 0.5s; }

/* Card flip effect */
.flip-card {
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
}

.flip-card-back {
    transform: rotateY(180deg);
}

/* Neon glow effect */
.neon-glow {
    text-shadow: 
        0 0 5px #3b82f6,
        0 0 10px #3b82f6,
        0 0 15px #3b82f6,
        0 0 20px #3b82f6;
}

/* Matrix rain effect */
.matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

/* Responsive typography */
@media (max-width: 640px) {
    .text-responsive {
        font-size: clamp(1rem, 4vw, 1.5rem);
    }
    
    .heading-responsive {
        font-size: clamp(2rem, 8vw, 4rem);
    }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
    body {
        color-scheme: dark;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gradient-text {
        background: white;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .glass-effect {
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid white;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .animate-float,
    .animate-bounce,
    .animate-spin {
        animation: none !important;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .bg-dark-bg,
    .bg-dark-secondary,
    .bg-dark-accent {
        background: white !important;
        color: black !important;
    }
}

/* Custom utilities */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.backdrop-blur-strong {
    backdrop-filter: blur(20px);
}

.border-gradient {
    border: 1px solid;
    border-image: linear-gradient(135deg, #3b82f6, #8b5cf6) 1;
}

/* Smooth transitions for all interactive elements */
a, button, input, textarea, select {
    transition: all 0.3s ease;
}

/* Custom focus ring */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Hover effects for cards */
.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Gradient borders */
.gradient-border {
    position: relative;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    padding: 2px;
    border-radius: 8px;
}

.gradient-border::before {
    content: '';
    position: absolute;
    inset: 2px;
    background: #1a1a1a;
    border-radius: 6px;
    z-index: -1;
}
