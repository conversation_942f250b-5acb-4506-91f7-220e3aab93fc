// Portfolio Configuration
// Update this file to easily customize your portfolio content

const portfolioConfig = {
    // Personal Information
    personal: {
        name: "Your Name",
        title: "Full Stack Developer",
        description: "Full Stack Developer passionate about creating exceptional digital experiences",
        email: "<EMAIL>",
        phone: "+****************",
        location: "Your City, Country",
        photo: "path/to/your/photo.jpg" // Add your photo path here
    },

    // Social Media Links
    social: {
        github: "https://github.com/yourusername",
        linkedin: "https://linkedin.com/in/yourusername",
        twitter: "https://twitter.com/yourusername",
        instagram: "https://instagram.com/yourusername"
    },

    // About Section
    about: {
        description: [
            "I'm a passionate developer with experience in building web applications from concept to deployment. I enjoy turning complex problems into simple, beautiful, and intuitive solutions.",
            "When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing my knowledge with the developer community."
        ],
        stats: {
            projects: "50+",
            experience: "3+"
        }
    },

    // Skills Configuration
    skills: {
        frontend: [
            { name: "HTML/CSS", level: 95, color: "accent-blue" },
            { name: "JavaScript", level: 90, color: "accent-blue" },
            { name: "React", level: 85, color: "accent-blue" }
        ],
        backend: [
            { name: "Node.js", level: 88, color: "accent-purple" },
            { name: "Python", level: 82, color: "accent-purple" },
            { name: "Express", level: 85, color: "accent-purple" }
        ],
        database: [
            { name: "MongoDB", level: 80, color: "green-400" },
            { name: "PostgreSQL", level: 75, color: "green-400" },
            { name: "MySQL", level: 78, color: "green-400" }
        ],
        tools: [
            { name: "Git", level: 92, color: "yellow-400" },
            { name: "Docker", level: 70, color: "yellow-400" },
            { name: "AWS", level: 65, color: "yellow-400" }
        ]
    },

    // Projects Configuration
    projects: [
        {
            title: "E-Commerce Platform",
            description: "A full-stack e-commerce solution with React frontend and Node.js backend, featuring user authentication, payment processing, and admin dashboard.",
            image: "path/to/project1-image.jpg",
            technologies: ["React", "Node.js", "MongoDB", "Stripe"],
            liveUrl: "https://your-project-demo.com",
            githubUrl: "https://github.com/yourusername/project1",
            featured: true
        },
        {
            title: "Task Management App",
            description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
            image: "path/to/project2-image.jpg",
            technologies: ["Vue.js", "Express", "Socket.io", "PostgreSQL"],
            liveUrl: "https://your-project-demo.com",
            githubUrl: "https://github.com/yourusername/project2",
            featured: true
        },
        {
            title: "Weather Dashboard",
            description: "A responsive weather dashboard that displays current weather conditions and forecasts using external APIs with beautiful data visualizations.",
            image: "path/to/project3-image.jpg",
            technologies: ["JavaScript", "Chart.js", "Weather API", "CSS3"],
            liveUrl: "https://your-project-demo.com",
            githubUrl: "https://github.com/yourusername/project3",
            featured: false
        }
    ],

    // Navigation Configuration
    navigation: [
        { name: "Home", href: "#home" },
        { name: "About", href: "#about" },
        { name: "Projects", href: "#projects" },
        { name: "Skills", href: "#skills" },
        { name: "Contact", href: "#contact" }
    ],

    // Theme Configuration
    theme: {
        colors: {
            primary: "#3b82f6",
            secondary: "#8b5cf6",
            background: "#0a0a0a",
            surface: "#1a1a1a",
            accent: "#2a2a2a"
        },
        fonts: {
            primary: "Inter, system-ui, sans-serif"
        }
    },

    // Animation Settings
    animations: {
        enabled: true,
        duration: {
            fast: "0.3s",
            normal: "0.6s",
            slow: "1s"
        },
        easing: "ease-out"
    },

    // SEO Configuration
    seo: {
        title: "Your Name - Portfolio",
        description: "Personal portfolio of [Your Name] - Full Stack Developer",
        keywords: "web developer, full stack, javascript, react, node.js, portfolio",
        author: "Your Name",
        url: "https://yourportfolio.com"
    },

    // Contact Form Configuration
    contact: {
        emailService: "emailjs", // or "netlify", "formspree", etc.
        serviceId: "your_service_id",
        templateId: "your_template_id",
        publicKey: "your_public_key"
    },

    // Feature Flags
    features: {
        darkMode: true,
        animations: true,
        particleBackground: false,
        typingAnimation: true,
        skillBars: true,
        contactForm: true,
        blogSection: false,
        testimonials: false
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = portfolioConfig;
}

// Make available globally in browser
if (typeof window !== 'undefined') {
    window.portfolioConfig = portfolioConfig;
}

// Helper functions for easy configuration updates
const updatePersonalInfo = (newInfo) => {
    Object.assign(portfolioConfig.personal, newInfo);
};

const addProject = (project) => {
    portfolioConfig.projects.push(project);
};

const updateSkill = (category, skillName, newLevel) => {
    const skill = portfolioConfig.skills[category].find(s => s.name === skillName);
    if (skill) {
        skill.level = newLevel;
    }
};

const toggleFeature = (featureName) => {
    if (portfolioConfig.features.hasOwnProperty(featureName)) {
        portfolioConfig.features[featureName] = !portfolioConfig.features[featureName];
    }
};

// Usage examples:
// updatePersonalInfo({ name: "John Doe", email: "<EMAIL>" });
// addProject({ title: "New Project", description: "...", technologies: [...] });
// updateSkill("frontend", "React", 95);
// toggleFeature("animations");

console.log("Portfolio configuration loaded successfully!");
