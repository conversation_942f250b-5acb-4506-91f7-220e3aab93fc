# Personal Portfolio Website

A modern, responsive portfolio website built with HTML, Tailwind CSS, and JavaScript featuring a sleek black theme and professional design.

## 🚀 Features

- **Responsive Design**: Looks great on all devices (desktop, tablet, mobile)
- **Dark Theme**: Professional black theme with blue and purple accents
- **Smooth Animations**: Scroll-triggered animations and smooth transitions
- **Interactive Navigation**: Fixed navbar with smooth scrolling and active link highlighting
- **Mobile-Friendly**: Hamburger menu for mobile devices
- **Contact Form**: Functional contact form with validation
- **Skills Showcase**: Animated skill bars and technology icons
- **Project Gallery**: Responsive project cards with hover effects
- **Performance Optimized**: Fast loading with optimized assets
- **Accessibility**: WCAG compliant with proper focus management

## 🛠️ Technologies Used

- **HTML5**: Semantic markup and structure
- **Tailwind CSS**: Utility-first CSS framework
- **JavaScript (ES6+)**: Interactive features and animations
- **Font Awesome**: Icons and visual elements
- **Google Fonts**: Inter font family

## 📁 Project Structure

```
portfolio/
├── index.html          # Main HTML file
├── script.js           # JavaScript functionality
├── styles.css          # Custom CSS styles
└── README.md           # Project documentation
```

## 🎨 Customization Guide

### 1. Personal Information

Update the following sections in `index.html`:

- **Name and Title**: Replace "Your Name" in the hero section
- **About Section**: Update the personal description and stats
- **Contact Information**: Update email, phone, and location
- **Social Media Links**: Add your actual social media profiles

### 2. Projects

Replace the placeholder project cards with your actual projects:

```html
<div class="bg-dark-secondary rounded-xl overflow-hidden hover-glow transition-all duration-300 group">
    <div class="h-48 bg-dark-accent flex items-center justify-center">
        <!-- Add your project image here -->
        <img src="your-project-image.jpg" alt="Project Name" class="w-full h-full object-cover">
    </div>
    <div class="p-6">
        <h3 class="text-xl font-semibold mb-2">Your Project Name</h3>
        <p class="text-gray-400 mb-4">Your project description</p>
        <div class="flex flex-wrap gap-2 mb-4">
            <span class="px-3 py-1 bg-dark-accent text-sm rounded-full">Technology</span>
        </div>
        <div class="flex gap-4">
            <a href="your-live-demo-url" class="text-accent-blue hover:text-blue-400">
                <i class="fas fa-external-link-alt mr-1"></i> Live Demo
            </a>
            <a href="your-github-url" class="text-accent-blue hover:text-blue-400">
                <i class="fab fa-github mr-1"></i> Code
            </a>
        </div>
    </div>
</div>
```

### 3. Skills

Update the skills section with your actual skills and proficiency levels:

```html
<div class="flex items-center justify-between">
    <span>Your Skill</span>
    <span class="text-accent-blue">85%</span>
</div>
<div class="w-full bg-dark-bg rounded-full h-2">
    <div class="bg-accent-blue h-2 rounded-full" style="width: 85%"></div>
</div>
```

### 4. Colors and Theme

The website uses a custom color palette defined in the Tailwind config:

- `dark-bg`: #0a0a0a (Main background)
- `dark-secondary`: #1a1a1a (Section backgrounds)
- `dark-accent`: #2a2a2a (Card backgrounds)
- `accent-blue`: #3b82f6 (Primary accent)
- `accent-purple`: #8b5cf6 (Secondary accent)

To change colors, update the Tailwind config in `index.html`:

```javascript
tailwind.config = {
    theme: {
        extend: {
            colors: {
                'dark-bg': '#your-color',
                'dark-secondary': '#your-color',
                // ... other colors
            }
        }
    }
}
```

### 5. Adding Your Photo

Replace the placeholder in the about section:

```html
<div class="w-80 h-80 mx-auto bg-dark-accent rounded-2xl overflow-hidden">
    <img src="your-photo.jpg" alt="Your Name" class="w-full h-full object-cover">
</div>
```

## 🚀 Getting Started

1. **Clone or Download**: Get the project files
2. **Customize Content**: Update personal information, projects, and skills
3. **Add Images**: Replace placeholder images with your actual photos and project screenshots
4. **Test Locally**: Open `index.html` in your browser to test
5. **Deploy**: Upload to your hosting provider

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Development

### Local Development

Simply open `index.html` in your browser. No build process required!

### Making Changes

1. Edit HTML content in `index.html`
2. Modify styles in `styles.css`
3. Update functionality in `script.js`
4. Refresh browser to see changes

## 📈 Performance Tips

- Optimize images (use WebP format when possible)
- Minimize custom CSS and JavaScript
- Use a CDN for faster loading
- Enable gzip compression on your server
- Consider lazy loading for images

## 🎯 SEO Optimization

- Update the `<title>` and meta description
- Add Open Graph tags for social media sharing
- Include structured data markup
- Optimize images with alt text
- Create a sitemap.xml

## 📞 Support

If you need help customizing this portfolio:

1. Check the customization guide above
2. Review the code comments for guidance
3. Test changes in small increments
4. Use browser developer tools for debugging

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Credits

- **Tailwind CSS**: For the utility-first CSS framework
- **Font Awesome**: For the beautiful icons
- **Google Fonts**: For the Inter font family
- **Inspiration**: Modern portfolio designs and best practices

---

**Happy coding!** 🚀

Remember to update this README with your specific information and any additional features you add to your portfolio.
